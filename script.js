// Language Switcher and Mobile Navigation
document.addEventListener('DOMContentLoaded', function() {
    // Language Switcher
    const langToggle = document.getElementById('langToggle');
    const currentLangSpan = document.getElementById('currentLang');
    let currentLanguage = 'en'; // Default language

    // Detect user's preferred language
    const userLang = navigator.language || navigator.userLanguage;
    if (userLang.startsWith('hi')) {
        currentLanguage = 'hi';
        currentLangSpan.textContent = 'HI';
        switchLanguage('hi');
    }

    langToggle.addEventListener('click', function() {
        currentLanguage = currentLanguage === 'en' ? 'hi' : 'en';
        currentLangSpan.textContent = currentLanguage.toUpperCase();
        switchLanguage(currentLanguage);
    });

    function switchLanguage(lang) {
        const elements = document.querySelectorAll('[data-en][data-hi]');
        elements.forEach(element => {
            if (element.tagName === 'INPUT' && element.type === 'email') {
                element.placeholder = element.getAttribute(`data-${lang}-placeholder`);
            } else {
                element.textContent = element.getAttribute(`data-${lang}`);
            }
        });

        // Update HTML lang attribute for SEO
        document.documentElement.lang = lang;

        // Update meta description for SEO
        const metaDesc = document.querySelector('meta[name="description"]');
        if (metaDesc) {
            if (lang === 'hi') {
                metaDesc.content = 'वेबसाइटडेवलपर0002 भारत भर में पेशेवर वेब डेवलपमेंट सेवाएं प्रदान करता है। HTML, CSS, JavaScript, PHP, और MySQL में विशेषज्ञता।';
            } else {
                metaDesc.content = 'WebsiteDeveloper0002 offers professional web development services across India. Specializing in HTML, CSS, JavaScript, PHP, and MySQL for businesses and service providers.';
            }
        }

        // Store language preference
        localStorage.setItem('preferredLanguage', lang);
    }

    // Load saved language preference
    const savedLang = localStorage.getItem('preferredLanguage');
    if (savedLang && savedLang !== currentLanguage) {
        currentLanguage = savedLang;
        currentLangSpan.textContent = currentLanguage.toUpperCase();
        switchLanguage(currentLanguage);
    }

    // Mobile Navigation Toggle
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });
    }

    // SEO-friendly smooth scrolling with history update
    function smoothScrollToSection(targetId) {
        const targetSection = document.querySelector(targetId);
        if (targetSection) {
            targetSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // Update URL without page reload for SEO
            if (history.pushState) {
                history.pushState(null, null, targetId);
            }
        }
    }

    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-menu a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            smoothScrollToSection(targetId);

            // Close mobile menu after clicking
            navMenu.classList.remove('active');
        });
    });

    // Handle browser back/forward buttons
    window.addEventListener('popstate', function(e) {
        if (location.hash) {
            smoothScrollToSection(location.hash);
        }
    });

    // Header scroll effect
    const header = document.querySelector('.header');
    let lastScrollTop = 0;
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > 100) {
            header.style.background = 'rgba(102, 126, 234, 0.95)';
            header.style.backdropFilter = 'blur(10px)';
        } else {
            header.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            header.style.backdropFilter = 'none';
        }
        
        lastScrollTop = scrollTop;
    });

    // Animate stats on scroll
    const statsSection = document.querySelector('.stats-section');
    const statNumbers = document.querySelectorAll('.stat-number');
    let statsAnimated = false;

    function animateStats() {
        if (statsAnimated) return;
        
        statNumbers.forEach(stat => {
            const finalNumber = parseInt(stat.textContent);
            let currentNumber = 0;
            const increment = finalNumber / 50;
            
            const timer = setInterval(() => {
                currentNumber += increment;
                if (currentNumber >= finalNumber) {
                    stat.textContent = finalNumber + '+';
                    clearInterval(timer);
                } else {
                    stat.textContent = Math.floor(currentNumber) + '+';
                }
            }, 30);
        });
        
        statsAnimated = true;
    }

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                if (entry.target === statsSection) {
                    animateStats();
                }
                
                // Add fade-in animation to feature cards
                if (entry.target.classList.contains('feature-card')) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            }
        });
    }, observerOptions);

    // Observe stats section
    if (statsSection) {
        observer.observe(statsSection);
    }

    // Observe feature cards
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });

    // Portfolio filtering functionality
    const filterButtons = document.querySelectorAll('.filter-btn');
    const portfolioItems = document.querySelectorAll('.portfolio-item, .portfolio-detail');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');

            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Filter portfolio items
            portfolioItems.forEach(item => {
                const category = item.getAttribute('data-category');

                if (filter === 'all' || category === filter) {
                    item.style.display = 'block';
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        item.style.transition = 'all 0.5s ease';
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, 100);
                } else {
                    item.style.transition = 'all 0.3s ease';
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(-20px)';

                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 300);
                }
            });
        });
    });

    // Newsletter subscription
    const newsletterForm = document.querySelector('.newsletter');
    if (newsletterForm) {
        const emailInput = newsletterForm.querySelector('input[type="email"]');
        const subscribeBtn = newsletterForm.querySelector('.btn');
        
        subscribeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const email = emailInput.value.trim();
            
            if (email && isValidEmail(email)) {
                // Simulate subscription
                subscribeBtn.textContent = 'Subscribed!';
                subscribeBtn.style.background = '#28a745';
                emailInput.value = '';
                
                setTimeout(() => {
                    subscribeBtn.textContent = 'Subscribe';
                    subscribeBtn.style.background = '';
                }, 3000);
            } else {
                emailInput.style.borderColor = '#dc3545';
                emailInput.placeholder = 'Please enter a valid email';
                
                setTimeout(() => {
                    emailInput.style.borderColor = '';
                    emailInput.placeholder = 'Enter your email';
                }, 3000);
            }
        });
    }

    // Email validation function
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Button hover effects
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Parallax effect for hero section
    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallax = heroSection.querySelector('.hero-content');
            const speed = scrolled * 0.5;
            
            if (parallax) {
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });
    }

    // Loading animation
    window.addEventListener('load', function() {
        document.body.classList.add('loaded');
        
        // Animate hero content on load
        const heroTitle = document.querySelector('.hero-title');
        const heroSubtitle = document.querySelector('.hero-subtitle');
        const heroButtons = document.querySelector('.hero-buttons');
        
        if (heroTitle) {
            heroTitle.style.opacity = '0';
            heroTitle.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                heroTitle.style.transition = 'all 0.8s ease';
                heroTitle.style.opacity = '1';
                heroTitle.style.transform = 'translateY(0)';
            }, 200);
        }
        
        if (heroSubtitle) {
            heroSubtitle.style.opacity = '0';
            heroSubtitle.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                heroSubtitle.style.transition = 'all 0.8s ease';
                heroSubtitle.style.opacity = '1';
                heroSubtitle.style.transform = 'translateY(0)';
            }, 400);
        }
        
        if (heroButtons) {
            heroButtons.style.opacity = '0';
            heroButtons.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                heroButtons.style.transition = 'all 0.8s ease';
                heroButtons.style.opacity = '1';
                heroButtons.style.transform = 'translateY(0)';
            }, 600);
        }
    });
});

// Add CSS for mobile menu
const style = document.createElement('style');
style.textContent = `
    @media (max-width: 768px) {
        .nav-menu {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            background: rgba(102, 126, 234, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            transform: translateY(-100%);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .nav-menu.active {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }

        .nav-menu ul {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }
    }
`;
document.head.appendChild(style);

// Schema.org structured data for SEO
const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "WebsiteDeveloper0002",
    "url": "https://websitedeveloper0002.in",
    "logo": "https://websitedeveloper0002.in/logo.png",
    "description": "Professional web development services in India specializing in HTML, CSS, JavaScript, PHP, and MySQL",
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "IN"
    },
    "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+91-9876543210",
        "contactType": "customer service",
        "email": "<EMAIL>"
    },
    "sameAs": [
        "https://facebook.com/websitedeveloper0002",
        "https://twitter.com/websitedeveloper0002",
        "https://linkedin.com/company/websitedeveloper0002"
    ],
    "offers": {
        "@type": "Offer",
        "description": "Web development services",
        "priceCurrency": "INR",
        "price": "15000"
    }
};

const script = document.createElement('script');
script.type = 'application/ld+json';
script.textContent = JSON.stringify(structuredData);
document.head.appendChild(script);
