<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio - WebsiteDeveloper0002 | Our Best Web Development Projects</title>
    <meta name="description" content="Explore our portfolio of successful web development projects. See how WebsiteDeveloper0002 has helped Indian businesses grow with custom websites.">
    <meta name="keywords" content="web development portfolio, website examples, HTML CSS projects, PHP MySQL websites, Indian business websites">
    <link rel="canonical" href="https://websitedeveloper0002.in/portfolio.html">
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <i class="fas fa-code"></i>
                <span data-en="WebsiteDeveloper0002" data-hi="वेबसाइटडेवलपर0002">WebsiteDeveloper0002</span>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.html#home" data-en="Home" data-hi="होम">Home</a></li>
                    <li><a href="index.html#about" data-en="About" data-hi="हमारे बारे में">About</a></li>
                    <li><a href="index.html#services" data-en="Services" data-hi="सेवाएं">Services</a></li>
                    <li><a href="portfolio.html" class="active" data-en="Portfolio" data-hi="पोर्टफोलियो">Portfolio</a></li>
                    <li><a href="index.html#pricing" data-en="Pricing" data-hi="मूल्य निर्धारण">Pricing</a></li>
                    <li><a href="blog.html" data-en="Blog" data-hi="ब्लॉग">Blog</a></li>
                    <li><a href="index.html#contact" data-en="Contact" data-hi="संपर्क">Contact</a></li>
                </ul>
            </nav>
            <div class="header-controls">
                <div class="language-switcher">
                    <button id="langToggle" class="lang-btn">
                        <i class="fas fa-globe"></i>
                        <span id="currentLang">EN</span>
                    </button>
                </div>
                <div class="nav-toggle">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Portfolio Hero Section -->
        <section class="portfolio-hero">
            <div class="container">
                <h1 data-en="Our Portfolio" data-hi="हमारा पोर्टफोलियो">Our Portfolio</h1>
                <p data-en="Showcasing successful web development projects that have helped Indian businesses grow and succeed online" data-hi="सफल वेब डेवलपमेंट परियोजनाओं का प्रदर्शन जिन्होंने भारतीय व्यवसायों को ऑनलाइन बढ़ने और सफल होने में मदद की है">Showcasing successful web development projects that have helped Indian businesses grow and succeed online</p>
            </div>
        </section>

        <!-- Portfolio Filters -->
        <section class="portfolio-main">
            <div class="container">
                <div class="portfolio-filters">
                    <button class="filter-btn active" data-filter="all" data-en="All Projects" data-hi="सभी परियोजनाएं">All Projects</button>
                    <button class="filter-btn" data-filter="ecommerce" data-en="E-commerce" data-hi="ई-कॉमर्स">E-commerce</button>
                    <button class="filter-btn" data-filter="business" data-en="Business" data-hi="व्यवसाय">Business</button>
                    <button class="filter-btn" data-filter="restaurant" data-en="Restaurant" data-hi="रेस्टोरेंट">Restaurant</button>
                    <button class="filter-btn" data-filter="healthcare" data-en="Healthcare" data-hi="स्वास्थ्य सेवा">Healthcare</button>
                    <button class="filter-btn" data-filter="education" data-en="Education" data-hi="शिक्षा">Education</button>
                </div>

                <!-- Portfolio Grid -->
                <div class="portfolio-grid-full">
                    <!-- Project 1 -->
                    <div id="project1" class="portfolio-detail" data-category="ecommerce">
                        <div class="project-header">
                            <div class="project-image">
                                <div class="placeholder-image">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span data-en="ShopIndia E-commerce Platform" data-hi="शॉपइंडिया ई-कॉमर्स प्लेटफॉर्म">ShopIndia E-commerce Platform</span>
                                </div>
                            </div>
                            <div class="project-info">
                                <h2 data-en="ShopIndia - Complete E-commerce Solution" data-hi="शॉपइंडिया - पूर्ण ई-कॉमर्स समाधान">ShopIndia - Complete E-commerce Solution</h2>
                                <div class="project-meta">
                                    <span class="project-category" data-en="E-commerce" data-hi="ई-कॉमर्स">E-commerce</span>
                                    <span class="project-date">Completed: February 2024</span>
                                </div>
                                <div class="project-tech">
                                    <span>PHP</span>
                                    <span>MySQL</span>
                                    <span>JavaScript</span>
                                    <span>HTML5</span>
                                    <span>CSS3</span>
                                </div>
                            </div>
                        </div>
                        <div class="project-description">
                            <h3 data-en="Project Overview" data-hi="परियोजना अवलोकन">Project Overview</h3>
                            <p data-en="ShopIndia is a comprehensive e-commerce platform designed for Indian retailers to sell their products online. The platform includes a complete admin panel, inventory management, payment gateway integration, and mobile-responsive design." data-hi="शॉपइंडिया भारतीय खुदरा विक्रेताओं के लिए डिज़ाइन किया गया एक व्यापक ई-कॉमर्स प्लेटफॉर्म है जो अपने उत्पादों को ऑनलाइन बेच सकते हैं। प्लेटफॉर्म में एक पूर्ण एडमिन पैनल, इन्वेंटरी प्रबंधन, पेमेंट गेटवे एकीकरण, और मोबाइल-रेस्पॉन्सिव डिज़ाइन शामिल है।">ShopIndia is a comprehensive e-commerce platform designed for Indian retailers to sell their products online. The platform includes a complete admin panel, inventory management, payment gateway integration, and mobile-responsive design.</p>
                            
                            <h4 data-en="Key Features" data-hi="मुख्य विशेषताएं">Key Features</h4>
                            <ul>
                                <li data-en="Multi-vendor marketplace functionality" data-hi="मल्टी-वेंडर मार्केटप्लेस कार्यक्षमता">Multi-vendor marketplace functionality</li>
                                <li data-en="Integrated payment gateway (Razorpay, PayU)" data-hi="एकीकृत पेमेंट गेटवे (Razorpay, PayU)">Integrated payment gateway (Razorpay, PayU)</li>
                                <li data-en="Real-time inventory management" data-hi="रियल-टाइम इन्वेंटरी प्रबंधन">Real-time inventory management</li>
                                <li data-en="Order tracking and management system" data-hi="ऑर्डर ट्रैकिंग और प्रबंधन सिस्टम">Order tracking and management system</li>
                                <li data-en="Customer review and rating system" data-hi="ग्राहक समीक्षा और रेटिंग सिस्टम">Customer review and rating system</li>
                                <li data-en="SEO-optimized product pages" data-hi="SEO-अनुकूलित उत्पाद पृष्ठ">SEO-optimized product pages</li>
                            </ul>

                            <h4 data-en="Results Achieved" data-hi="प्राप्त परिणाम">Results Achieved</h4>
                            <ul>
                                <li data-en="300% increase in online sales within 6 months" data-hi="6 महीने के भीतर ऑनलाइन बिक्री में 300% वृद्धि">300% increase in online sales within 6 months</li>
                                <li data-en="50+ vendors onboarded successfully" data-hi="50+ विक्रेता सफलतापूर्वक ऑनबोर्ड किए गए">50+ vendors onboarded successfully</li>
                                <li data-en="Mobile traffic increased by 250%" data-hi="मोबाइल ट्रैफिक में 250% की वृद्धि">Mobile traffic increased by 250%</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Project 2 -->
                    <div id="project2" class="portfolio-detail" data-category="business">
                        <div class="project-header">
                            <div class="project-image">
                                <div class="placeholder-image">
                                    <i class="fas fa-building"></i>
                                    <span data-en="TechSolutions Corporate Website" data-hi="टेकसॉल्यूशन्स कॉर्पोरेट वेबसाइट">TechSolutions Corporate Website</span>
                                </div>
                            </div>
                            <div class="project-info">
                                <h2 data-en="TechSolutions India - Corporate Website" data-hi="टेकसॉल्यूशन्स इंडिया - कॉर्पोरेट वेबसाइट">TechSolutions India - Corporate Website</h2>
                                <div class="project-meta">
                                    <span class="project-category" data-en="Business" data-hi="व्यवसाय">Business</span>
                                    <span class="project-date">Completed: January 2024</span>
                                </div>
                                <div class="project-tech">
                                    <span>HTML5</span>
                                    <span>CSS3</span>
                                    <span>JavaScript</span>
                                    <span>PHP</span>
                                    <span>MySQL</span>
                                </div>
                            </div>
                        </div>
                        <div class="project-description">
                            <h3 data-en="Project Overview" data-hi="परियोजना अवलोकन">Project Overview</h3>
                            <p data-en="A professional corporate website for TechSolutions India, a leading IT consulting firm. The website features a modern design, content management system, and client portal for project tracking." data-hi="टेकसॉल्यूशन्स इंडिया के लिए एक पेशेवर कॉर्पोरेट वेबसाइट, जो एक अग्रणी आईटी कंसल्टिंग फर्म है। वेबसाइट में एक आधुनिक डिज़ाइन, कंटेंट मैनेजमेंट सिस्टम, और प्रोजेक्ट ट्रैकिंग के लिए क्लाइंट पोर्टल है।">A professional corporate website for TechSolutions India, a leading IT consulting firm. The website features a modern design, content management system, and client portal for project tracking.</p>
                            
                            <h4 data-en="Key Features" data-hi="मुख्य विशेषताएं">Key Features</h4>
                            <ul>
                                <li data-en="Custom CMS for easy content updates" data-hi="आसान कंटेंट अपडेट के लिए कस्टम CMS">Custom CMS for easy content updates</li>
                                <li data-en="Client portal with project tracking" data-hi="प्रोजेक्ट ट्रैकिंग के साथ क्लाइंट पोर्टल">Client portal with project tracking</li>
                                <li data-en="Team member profiles and expertise showcase" data-hi="टीम सदस्य प्रोफाइल और विशेषज्ञता प्रदर्शन">Team member profiles and expertise showcase</li>
                                <li data-en="Case studies and success stories section" data-hi="केस स्टडी और सफलता की कहानियां अनुभाग">Case studies and success stories section</li>
                                <li data-en="Contact forms with lead management" data-hi="लीड प्रबंधन के साथ संपर्क फॉर्म">Contact forms with lead management</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Project 3 -->
                    <div id="project3" class="portfolio-detail" data-category="restaurant">
                        <div class="project-header">
                            <div class="project-image">
                                <div class="placeholder-image">
                                    <i class="fas fa-utensils"></i>
                                    <span data-en="Spice Garden Restaurant Chain" data-hi="स्पाइस गार्डन रेस्टोरेंट चेन">Spice Garden Restaurant Chain</span>
                                </div>
                            </div>
                            <div class="project-info">
                                <h2 data-en="Spice Garden - Multi-Location Restaurant Website" data-hi="स्पाइस गार्डन - मल्टी-लोकेशन रेस्टोरेंट वेबसाइट">Spice Garden - Multi-Location Restaurant Website</h2>
                                <div class="project-meta">
                                    <span class="project-category" data-en="Restaurant" data-hi="रेस्टोरेंट">Restaurant</span>
                                    <span class="project-date">Completed: December 2023</span>
                                </div>
                                <div class="project-tech">
                                    <span>JavaScript</span>
                                    <span>PHP</span>
                                    <span>MySQL</span>
                                    <span>HTML5</span>
                                    <span>CSS3</span>
                                </div>
                            </div>
                        </div>
                        <div class="project-description">
                            <h3 data-en="Project Overview" data-hi="परियोजना अवलोकन">Project Overview</h3>
                            <p data-en="A comprehensive website for Spice Garden restaurant chain with online ordering system, table reservations, and location-based menu management across multiple cities in India." data-hi="स्पाइस गार्डन रेस्टोरेंट चेन के लिए एक व्यापक वेबसाइट जिसमें ऑनलाइन ऑर्डरिंग सिस्टम, टेबल रिज़र्वेशन, और भारत के कई शहरों में स्थान-आधारित मेनू प्रबंधन है।">A comprehensive website for Spice Garden restaurant chain with online ordering system, table reservations, and location-based menu management across multiple cities in India.</p>
                            
                            <h4 data-en="Key Features" data-hi="मुख्य विशेषताएं">Key Features</h4>
                            <ul>
                                <li data-en="Online food ordering with delivery tracking" data-hi="डिलीवरी ट्रैकिंग के साथ ऑनलाइन फूड ऑर्डरिंग">Online food ordering with delivery tracking</li>
                                <li data-en="Table reservation system" data-hi="टेबल रिज़र्वेशन सिस्टम">Table reservation system</li>
                                <li data-en="Location-based menu and pricing" data-hi="स्थान-आधारित मेनू और मूल्य निर्धारण">Location-based menu and pricing</li>
                                <li data-en="Customer loyalty program integration" data-hi="ग्राहक वफादारी कार्यक्रम एकीकरण">Customer loyalty program integration</li>
                                <li data-en="Multi-language support (Hindi, English)" data-hi="बहु-भाषा समर्थन (हिंदी, अंग्रेजी)">Multi-language support (Hindi, English)</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Project 4 -->
                    <div id="project4" class="portfolio-detail" data-category="healthcare">
                        <div class="project-header">
                            <div class="project-image">
                                <div class="placeholder-image">
                                    <i class="fas fa-heartbeat"></i>
                                    <span data-en="HealthCare Plus Medical Portal" data-hi="हेल्थकेयर प्लस मेडिकल पोर्टल">HealthCare Plus Medical Portal</span>
                                </div>
                            </div>
                            <div class="project-info">
                                <h2 data-en="HealthCare Plus - Comprehensive Medical Portal" data-hi="हेल्थकेयर प्लस - व्यापक मेडिकल पोर्टल">HealthCare Plus - Comprehensive Medical Portal</h2>
                                <div class="project-meta">
                                    <span class="project-category" data-en="Healthcare" data-hi="स्वास्थ्य सेवा">Healthcare</span>
                                    <span class="project-date">Completed: November 2023</span>
                                </div>
                                <div class="project-tech">
                                    <span>PHP</span>
                                    <span>MySQL</span>
                                    <span>JavaScript</span>
                                    <span>Bootstrap</span>
                                    <span>Chart.js</span>
                                </div>
                            </div>
                        </div>
                        <div class="project-description">
                            <h3 data-en="Project Overview" data-hi="परियोजना अवलोकन">Project Overview</h3>
                            <p data-en="HealthCare Plus is a comprehensive medical portal designed for healthcare providers in India. The platform connects patients with doctors, manages appointments, and provides telemedicine capabilities." data-hi="हेल्थकेयर प्लस भारत में स्वास्थ्य सेवा प्रदाताओं के लिए डिज़ाइन किया गया एक व्यापक मेडिकल पोर्टल है। प्लेटफॉर्म मरीजों को डॉक्टरों से जोड़ता है, अपॉइंटमेंट प्रबंधित करता है, और टेलीमेडिसिन क्षमताएं प्रदान करता है।">HealthCare Plus is a comprehensive medical portal designed for healthcare providers in India. The platform connects patients with doctors, manages appointments, and provides telemedicine capabilities.</p>

                            <h4 data-en="Key Features" data-hi="मुख्य विशेषताएं">Key Features</h4>
                            <ul>
                                <li data-en="Online appointment booking system" data-hi="ऑनलाइन अपॉइंटमेंट बुकिंग सिस्टम">Online appointment booking system</li>
                                <li data-en="Patient medical records management" data-hi="मरीज़ के मेडिकल रिकॉर्ड प्रबंधन">Patient medical records management</li>
                                <li data-en="Telemedicine video consultation" data-hi="टेलीमेडिसिन वीडियो परामर्श">Telemedicine video consultation</li>
                                <li data-en="Prescription management system" data-hi="प्रिस्क्रिप्शन प्रबंधन सिस्टम">Prescription management system</li>
                                <li data-en="Doctor availability calendar" data-hi="डॉक्टर उपलब्धता कैलेंडर">Doctor availability calendar</li>
                                <li data-en="Payment gateway integration" data-hi="पेमेंट गेटवे एकीकरण">Payment gateway integration</li>
                            </ul>

                            <h4 data-en="Results Achieved" data-hi="प्राप्त परिणाम">Results Achieved</h4>
                            <ul>
                                <li data-en="200+ doctors registered on the platform" data-hi="प्लेटफॉर्म पर 200+ डॉक्टर पंजीकृत">200+ doctors registered on the platform</li>
                                <li data-en="5000+ patients using the service" data-hi="5000+ मरीज़ सेवा का उपयोग कर रहे हैं">5000+ patients using the service</li>
                                <li data-en="40% reduction in appointment no-shows" data-hi="अपॉइंटमेंट न आने में 40% कमी">40% reduction in appointment no-shows</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Project 5 -->
                    <div id="project5" class="portfolio-detail" data-category="education">
                        <div class="project-header">
                            <div class="project-image">
                                <div class="placeholder-image">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span data-en="EduIndia Learning Platform" data-hi="एडुइंडिया लर्निंग प्लेटफॉर्म">EduIndia Learning Platform</span>
                                </div>
                            </div>
                            <div class="project-info">
                                <h2 data-en="EduIndia - Complete Learning Management System" data-hi="एडुइंडिया - पूर्ण लर्निंग मैनेजमेंट सिस्टम">EduIndia - Complete Learning Management System</h2>
                                <div class="project-meta">
                                    <span class="project-category" data-en="Education" data-hi="शिक्षा">Education</span>
                                    <span class="project-date">Completed: October 2023</span>
                                </div>
                                <div class="project-tech">
                                    <span>PHP</span>
                                    <span>MySQL</span>
                                    <span>JavaScript</span>
                                    <span>Video.js</span>
                                    <span>Chart.js</span>
                                </div>
                            </div>
                        </div>
                        <div class="project-description">
                            <h3 data-en="Project Overview" data-hi="परियोजना अवलोकन">Project Overview</h3>
                            <p data-en="EduIndia is a comprehensive learning management system designed for educational institutions in India. The platform supports online courses, student management, and progress tracking." data-hi="एडुइंडिया भारत में शैक्षणिक संस्थानों के लिए डिज़ाइन किया गया एक व्यापक लर्निंग मैनेजमेंट सिस्टम है। प्लेटफॉर्म ऑनलाइन कोर्स, छात्र प्रबंधन, और प्रगति ट्रैकिंग का समर्थन करता है।">EduIndia is a comprehensive learning management system designed for educational institutions in India. The platform supports online courses, student management, and progress tracking.</p>

                            <h4 data-en="Key Features" data-hi="मुख्य विशेषताएं">Key Features</h4>
                            <ul>
                                <li data-en="Course creation and management tools" data-hi="कोर्स निर्माण और प्रबंधन उपकरण">Course creation and management tools</li>
                                <li data-en="Student enrollment and progress tracking" data-hi="छात्र नामांकन और प्रगति ट्रैकिंग">Student enrollment and progress tracking</li>
                                <li data-en="Video lecture streaming platform" data-hi="वीडियो लेक्चर स्ट्रीमिंग प्लेटफॉर्म">Video lecture streaming platform</li>
                                <li data-en="Assignment submission and grading" data-hi="असाइनमेंट सबमिशन और ग्रेडिंग">Assignment submission and grading</li>
                                <li data-en="Discussion forums and chat" data-hi="चर्चा फोरम और चैट">Discussion forums and chat</li>
                                <li data-en="Certificate generation system" data-hi="प्रमाणपत्र जनरेशन सिस्टम">Certificate generation system</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Project 6 -->
                    <div id="project6" class="portfolio-detail" data-category="ecommerce">
                        <div class="project-header">
                            <div class="project-image">
                                <div class="placeholder-image">
                                    <i class="fas fa-mobile-alt"></i>
                                    <span data-en="FoodDelivery App Landing" data-hi="फूडडिलीवरी ऐप लैंडिंग">FoodDelivery App Landing</span>
                                </div>
                            </div>
                            <div class="project-info">
                                <h2 data-en="FoodDelivery App - Modern Landing Page" data-hi="फूडडिलीवरी ऐप - आधुनिक लैंडिंग पेज">FoodDelivery App - Modern Landing Page</h2>
                                <div class="project-meta">
                                    <span class="project-category" data-en="E-commerce" data-hi="ई-कॉमर्स">E-commerce</span>
                                    <span class="project-date">Completed: September 2023</span>
                                </div>
                                <div class="project-tech">
                                    <span>HTML5</span>
                                    <span>CSS3</span>
                                    <span>JavaScript</span>
                                    <span>GSAP</span>
                                    <span>AOS</span>
                                </div>
                            </div>
                        </div>
                        <div class="project-description">
                            <h3 data-en="Project Overview" data-hi="परियोजना अवलोकन">Project Overview</h3>
                            <p data-en="A modern, animated landing page for a food delivery mobile application targeting Indian markets. The page features smooth animations, mobile-first design, and conversion-optimized layout." data-hi="भारतीय बाजारों को लक्षित करने वाले फूड डिलीवरी मोबाइल एप्लिकेशन के लिए एक आधुनिक, एनिमेटेड लैंडिंग पेज। पेज में स्मूथ एनिमेशन, मोबाइल-फर्स्ट डिज़ाइन, और कन्वर्जन-अनुकूलित लेआउट है।">A modern, animated landing page for a food delivery mobile application targeting Indian markets. The page features smooth animations, mobile-first design, and conversion-optimized layout.</p>

                            <h4 data-en="Key Features" data-hi="मुख्य विशेषताएं">Key Features</h4>
                            <ul>
                                <li data-en="Smooth scroll animations and micro-interactions" data-hi="स्मूथ स्क्रॉल एनिमेशन और माइक्रो-इंटरैक्शन">Smooth scroll animations and micro-interactions</li>
                                <li data-en="App store download tracking" data-hi="ऐप स्टोर डाउनलोड ट्रैकिंग">App store download tracking</li>
                                <li data-en="Restaurant partner showcase" data-hi="रेस्टोरेंट पार्टनर शोकेस">Restaurant partner showcase</li>
                                <li data-en="Customer testimonials carousel" data-hi="ग्राहक प्रशंसापत्र कैरोसेल">Customer testimonials carousel</li>
                                <li data-en="City-wise service area display" data-hi="शहर-वार सेवा क्षेत्र प्रदर्शन">City-wise service area display</li>
                            </ul>

                            <h4 data-en="Results Achieved" data-hi="प्राप्त परिणाम">Results Achieved</h4>
                            <ul>
                                <li data-en="150% increase in app downloads" data-hi="ऐप डाउनलोड में 150% वृद्धि">150% increase in app downloads</li>
                                <li data-en="65% improvement in conversion rate" data-hi="कन्वर्जन रेट में 65% सुधार">65% improvement in conversion rate</li>
                                <li data-en="Mobile traffic increased by 200%" data-hi="मोबाइल ट्रैफिक में 200% की वृद्धि">Mobile traffic increased by 200%</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Section -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3 data-en="Company" data-hi="कंपनी">Company</h3>
                    <ul>
                        <li><a href="index.html#about" data-en="About Us" data-hi="हमारे बारे में">About Us</a></li>
                        <li><a href="index.html#services" data-en="Our Services" data-hi="हमारी सेवाएं">Our Services</a></li>
                        <li><a href="portfolio.html" data-en="Portfolio" data-hi="पोर्टफोलियो">Portfolio</a></li>
                        <li><a href="blog.html" data-en="Blog" data-hi="ब्लॉग">Blog</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3 data-en="Services" data-hi="सेवाएं">Services</h3>
                    <ul>
                        <li><a href="#" data-en="HTML5 Development" data-hi="HTML5 डेवलपमेंट">HTML5 Development</a></li>
                        <li><a href="#" data-en="CSS3 Styling" data-hi="CSS3 स्टाइलिंग">CSS3 Styling</a></li>
                        <li><a href="#" data-en="JavaScript Development" data-hi="जावास्क्रिप्ट डेवलपमेंट">JavaScript Development</a></li>
                        <li><a href="#" data-en="PHP Backend" data-hi="PHP बैकएंड">PHP Backend</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3 data-en="Legal and Compliance" data-hi="कानूनी और अनुपालन">Legal and Compliance</h3>
                    <ul>
                        <li><a href="#" data-en="Privacy Policy" data-hi="गोपनीयता नीति">Privacy Policy</a></li>
                        <li><a href="#" data-en="Terms of Service" data-hi="सेवा की शर्तें">Terms of Service</a></li>
                        <li><a href="#" data-en="Refund Policy" data-hi="रिफंड नीति">Refund Policy</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3 data-en="Connect With Us" data-hi="हमसे जुड़ें">Connect With Us</h3>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-phone"></i> +91 9876543210</p>
                    </div>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 <span data-en="WebsiteDeveloper0002. All rights reserved." data-hi="वेबसाइटडेवलपर0002। सभी अधिकार सुरक्षित।">WebsiteDeveloper0002. All rights reserved.</span></p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
