// Admin Dashboard JavaScript

// Check authentication on page load
document.addEventListener('DOMContentLoaded', function() {
    if (sessionStorage.getItem('adminLoggedIn') !== 'true') {
        window.location.href = 'login.html';
        return;
    }
    
    // Initialize dashboard
    initializeDashboard();
});

// Initialize dashboard data
function initializeDashboard() {
    // Load sample data (in production, this would come from a database)
    loadDashboardStats();
    loadRecentActivity();
}

// Navigation functions
function showSection(sectionId) {
    // Hide all sections
    const sections = document.querySelectorAll('.admin-section');
    sections.forEach(section => section.classList.remove('active'));
    
    // Show selected section
    document.getElementById(sectionId).classList.add('active');
    
    // Update active menu item
    const menuItems = document.querySelectorAll('.admin-menu li');
    menuItems.forEach(item => item.classList.remove('active'));
    
    const activeMenuItem = document.querySelector(`[onclick="showSection('${sectionId}')"]`).parentElement;
    activeMenuItem.classList.add('active');
}

// Logout function
function logout() {
    sessionStorage.removeItem('adminLoggedIn');
    sessionStorage.removeItem('adminUser');
    window.location.href = 'login.html';
}

// Dashboard functions
function loadDashboardStats() {
    // Sample data - in production, fetch from database
    document.getElementById('totalBlogs').textContent = '12';
    document.getElementById('totalPortfolio').textContent = '8';
    document.getElementById('totalContacts').textContent = '45';
    document.getElementById('totalSubscribers').textContent = '156';
}

function loadRecentActivity() {
    // Sample recent activity data
    const activities = [
        {
            icon: 'fas fa-envelope',
            text: '<strong>New contact query</strong> from Rajesh Kumar',
            time: '2 hours ago'
        },
        {
            icon: 'fas fa-user-plus',
            text: '<strong>New newsletter subscription</strong> from <EMAIL>',
            time: '5 hours ago'
        },
        {
            icon: 'fas fa-mail-bulk',
            text: '<strong>Email sent</strong> - Thank you email to new contact',
            time: '1 day ago'
        }
    ];
    
    // Update activity list (already populated in HTML for demo)
}

// Blog management functions
function showAddBlogForm() {
    alert('Add Blog Form - This would open a modal or redirect to a form page');
    // In production, this would open a modal or redirect to a form
}

function editBlog(blogId) {
    alert(`Edit Blog ID: ${blogId} - This would open the edit form`);
    // In production, this would open an edit form with the blog data
}

function deleteBlog(blogId) {
    if (confirm('Are you sure you want to delete this blog post?')) {
        alert(`Blog ID: ${blogId} deleted`);
        // In production, this would make an API call to delete the blog
        // Then refresh the table
    }
}

// Portfolio management functions
function showAddPortfolioForm() {
    alert('Add Portfolio Form - This would open a modal or redirect to a form page');
    // In production, this would open a modal or redirect to a form
}

function editPortfolio(portfolioId) {
    alert(`Edit Portfolio ID: ${portfolioId} - This would open the edit form`);
    // In production, this would open an edit form with the portfolio data
}

function deletePortfolio(portfolioId) {
    if (confirm('Are you sure you want to delete this portfolio project?')) {
        alert(`Portfolio ID: ${portfolioId} deleted`);
        // In production, this would make an API call to delete the portfolio
        // Then refresh the table
    }
}

// Contact management functions
function filterContacts(status) {
    // Update active filter button
    const filterButtons = document.querySelectorAll('.filter-buttons .btn');
    filterButtons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    // Filter table rows based on status
    const rows = document.querySelectorAll('#contactsTable tr');
    rows.forEach(row => {
        const statusElement = row.querySelector('.status');
        if (!statusElement) return;
        
        const rowStatus = statusElement.textContent.toLowerCase();
        
        if (status === 'all' || rowStatus === status) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function viewContact(contactId) {
    alert(`View Contact ID: ${contactId} - This would show contact details in a modal`);
    // In production, this would open a modal with full contact details
}

function replyContact(contactId) {
    alert(`Reply to Contact ID: ${contactId} - This would open email compose modal`);
    // In production, this would open an email compose modal
}

function deleteContact(contactId) {
    if (confirm('Are you sure you want to delete this contact query?')) {
        alert(`Contact ID: ${contactId} deleted`);
        // In production, this would make an API call to delete the contact
        // Then refresh the table
    }
}

// Newsletter management functions
function exportSubscribers() {
    alert('Export Subscribers - This would download a CSV file');
    // In production, this would generate and download a CSV file
}

function sendEmail(email) {
    alert(`Send Email to: ${email} - This would open email compose modal`);
    // In production, this would open an email compose modal
}

function removeSubscriber(email) {
    if (confirm(`Are you sure you want to remove ${email} from the newsletter?`)) {
        alert(`${email} removed from newsletter`);
        // In production, this would make an API call to remove the subscriber
        // Then refresh the table
    }
}

// Email management functions (to be implemented)
function showEmailSection() {
    // This would show email management interface
    const emailSection = `
        <div class="section-header">
            <h1><i class="fas fa-mail-bulk"></i> Email Management</h1>
            <button class="btn btn-primary" onclick="composeEmail()">
                <i class="fas fa-plus"></i>
                Compose Email
            </button>
        </div>
        
        <div class="email-stats">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-paper-plane"></i>
                </div>
                <div class="stat-info">
                    <h3>450</h3>
                    <p>Emails Sent This Month</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-envelope-open"></i>
                </div>
                <div class="stat-info">
                    <h3>85%</h3>
                    <p>Open Rate</p>
                </div>
            </div>
        </div>
        
        <div class="email-templates">
            <h2>Email Templates</h2>
            <div class="template-grid">
                <div class="template-card">
                    <h3>Thank You Email</h3>
                    <p>Sent automatically when someone contacts us</p>
                    <button class="btn btn-secondary" onclick="editTemplate('thank-you')">Edit</button>
                </div>
                
                <div class="template-card">
                    <h3>Newsletter Welcome</h3>
                    <p>Sent to new newsletter subscribers</p>
                    <button class="btn btn-secondary" onclick="editTemplate('newsletter-welcome')">Edit</button>
                </div>
            </div>
        </div>
    `;
    
    // This would be added to the emails section
}

function composeEmail() {
    alert('Compose Email - This would open email composition interface');
    // In production, this would open a rich text editor for composing emails
}

function editTemplate(templateId) {
    alert(`Edit Template: ${templateId} - This would open template editor`);
    // In production, this would open a template editor
}

// Email reports functions
function showReportsSection() {
    // This would show email reporting interface with charts and analytics
    alert('Email Reports - This would show detailed email analytics');
}

// Utility functions
function formatDate(date) {
    return new Date(date).toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
        ${message}
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Auto-save functionality for forms
function autoSave(formData, type) {
    // Save form data to localStorage as backup
    localStorage.setItem(`autosave_${type}`, JSON.stringify(formData));
}

// Search functionality
function searchTable(tableId, searchTerm) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm.toLowerCase())) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Mobile menu toggle
function toggleMobileMenu() {
    const sidebar = document.querySelector('.admin-sidebar');
    sidebar.classList.toggle('mobile-open');
}

// Add mobile menu styles
const mobileStyles = `
    @media (max-width: 768px) {
        .admin-sidebar.mobile-open {
            transform: translateX(0);
        }
        
        .mobile-menu-toggle {
            display: block;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #333;
            cursor: pointer;
        }
    }
    
    .mobile-menu-toggle {
        display: none;
    }
    
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        z-index: 9999;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .notification.error {
        background: #dc3545;
    }
`;

// Add styles to head
const styleSheet = document.createElement('style');
styleSheet.textContent = mobileStyles;
document.head.appendChild(styleSheet);
