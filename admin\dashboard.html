<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - WebsiteDeveloper0002</title>
    <meta name="robots" content="noindex, nofollow">
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="admin-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="admin-body">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-nav">
            <div class="admin-logo">
                <i class="fas fa-shield-alt"></i>
                <span>Admin Panel</span>
            </div>
            <div class="admin-user">
                <span>Welcome, Admin</span>
                <button onclick="logout()" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </button>
            </div>
        </div>
    </header>

    <!-- Admin Sidebar -->
    <aside class="admin-sidebar">
        <nav class="admin-menu">
            <ul>
                <li class="active">
                    <a href="#dashboard" onclick="showSection('dashboard')">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                </li>
                <li>
                    <a href="#blogs" onclick="showSection('blogs')">
                        <i class="fas fa-blog"></i>
                        Manage Blogs
                    </a>
                </li>
                <li>
                    <a href="#portfolio" onclick="showSection('portfolio')">
                        <i class="fas fa-briefcase"></i>
                        Manage Portfolio
                    </a>
                </li>
                <li>
                    <a href="#contacts" onclick="showSection('contacts')">
                        <i class="fas fa-envelope"></i>
                        Contact Queries
                    </a>
                </li>
                <li>
                    <a href="#newsletter" onclick="showSection('newsletter')">
                        <i class="fas fa-users"></i>
                        Newsletter Subscribers
                    </a>
                </li>
                <li>
                    <a href="#emails" onclick="showSection('emails')">
                        <i class="fas fa-mail-bulk"></i>
                        Email Management
                    </a>
                </li>
                <li>
                    <a href="#reports" onclick="showSection('reports')">
                        <i class="fas fa-chart-bar"></i>
                        Email Reports
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <!-- Dashboard Section -->
        <section id="dashboard" class="admin-section active">
            <div class="section-header">
                <h1><i class="fas fa-tachometer-alt"></i> Dashboard Overview</h1>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-blog"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalBlogs">12</h3>
                        <p>Total Blog Posts</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalPortfolio">8</h3>
                        <p>Portfolio Projects</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalContacts">45</h3>
                        <p>Contact Queries</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalSubscribers">156</h3>
                        <p>Newsletter Subscribers</p>
                    </div>
                </div>
            </div>
            
            <div class="recent-activity">
                <h2>Recent Activity</h2>
                <div class="activity-list">
                    <div class="activity-item">
                        <i class="fas fa-envelope"></i>
                        <div class="activity-content">
                            <p><strong>New contact query</strong> from Rajesh Kumar</p>
                            <span class="activity-time">2 hours ago</span>
                        </div>
                    </div>
                    <div class="activity-item">
                        <i class="fas fa-user-plus"></i>
                        <div class="activity-content">
                            <p><strong>New newsletter subscription</strong> from <EMAIL></p>
                            <span class="activity-time">5 hours ago</span>
                        </div>
                    </div>
                    <div class="activity-item">
                        <i class="fas fa-mail-bulk"></i>
                        <div class="activity-content">
                            <p><strong>Email sent</strong> - Thank you email to new contact</p>
                            <span class="activity-time">1 day ago</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Blogs Section -->
        <section id="blogs" class="admin-section">
            <div class="section-header">
                <h1><i class="fas fa-blog"></i> Manage Blogs</h1>
                <button class="btn btn-primary" onclick="showAddBlogForm()">
                    <i class="fas fa-plus"></i>
                    Add New Blog
                </button>
            </div>
            
            <div class="content-table">
                <table>
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="blogsTable">
                        <tr>
                            <td>Top 10 Web Development Trends in 2024</td>
                            <td>Web Development</td>
                            <td>March 15, 2024</td>
                            <td><span class="status published">Published</span></td>
                            <td>
                                <button class="btn-icon" onclick="editBlog(1)"><i class="fas fa-edit"></i></button>
                                <button class="btn-icon delete" onclick="deleteBlog(1)"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td>Local SEO Optimization in India</td>
                            <td>SEO</td>
                            <td>March 10, 2024</td>
                            <td><span class="status published">Published</span></td>
                            <td>
                                <button class="btn-icon" onclick="editBlog(2)"><i class="fas fa-edit"></i></button>
                                <button class="btn-icon delete" onclick="deleteBlog(2)"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Portfolio Section -->
        <section id="portfolio" class="admin-section">
            <div class="section-header">
                <h1><i class="fas fa-briefcase"></i> Manage Portfolio</h1>
                <button class="btn btn-primary" onclick="showAddPortfolioForm()">
                    <i class="fas fa-plus"></i>
                    Add New Project
                </button>
            </div>
            
            <div class="content-table">
                <table>
                    <thead>
                        <tr>
                            <th>Project Name</th>
                            <th>Category</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="portfolioTable">
                        <tr>
                            <td>ShopIndia - E-commerce Platform</td>
                            <td>E-commerce</td>
                            <td>February 2024</td>
                            <td><span class="status published">Published</span></td>
                            <td>
                                <button class="btn-icon" onclick="editPortfolio(1)"><i class="fas fa-edit"></i></button>
                                <button class="btn-icon delete" onclick="deletePortfolio(1)"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td>TechSolutions - Corporate Website</td>
                            <td>Business</td>
                            <td>January 2024</td>
                            <td><span class="status published">Published</span></td>
                            <td>
                                <button class="btn-icon" onclick="editPortfolio(2)"><i class="fas fa-edit"></i></button>
                                <button class="btn-icon delete" onclick="deletePortfolio(2)"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Contact Queries Section -->
        <section id="contacts" class="admin-section">
            <div class="section-header">
                <h1><i class="fas fa-envelope"></i> Contact Queries</h1>
                <div class="filter-buttons">
                    <button class="btn btn-secondary active" onclick="filterContacts('all')">All</button>
                    <button class="btn btn-secondary" onclick="filterContacts('new')">New</button>
                    <button class="btn btn-secondary" onclick="filterContacts('replied')">Replied</button>
                </div>
            </div>
            
            <div class="content-table">
                <table>
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Subject</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="contactsTable">
                        <tr>
                            <td>Rajesh Kumar</td>
                            <td><EMAIL></td>
                            <td>Website Development Inquiry</td>
                            <td>March 16, 2024</td>
                            <td><span class="status new">New</span></td>
                            <td>
                                <button class="btn-icon" onclick="viewContact(1)"><i class="fas fa-eye"></i></button>
                                <button class="btn-icon" onclick="replyContact(1)"><i class="fas fa-reply"></i></button>
                                <button class="btn-icon delete" onclick="deleteContact(1)"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td>Priya Sharma</td>
                            <td><EMAIL></td>
                            <td>E-commerce Package Query</td>
                            <td>March 15, 2024</td>
                            <td><span class="status replied">Replied</span></td>
                            <td>
                                <button class="btn-icon" onclick="viewContact(2)"><i class="fas fa-eye"></i></button>
                                <button class="btn-icon" onclick="replyContact(2)"><i class="fas fa-reply"></i></button>
                                <button class="btn-icon delete" onclick="deleteContact(2)"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Newsletter Subscribers Section -->
        <section id="newsletter" class="admin-section">
            <div class="section-header">
                <h1><i class="fas fa-users"></i> Newsletter Subscribers</h1>
                <button class="btn btn-primary" onclick="exportSubscribers()">
                    <i class="fas fa-download"></i>
                    Export List
                </button>
            </div>
            
            <div class="content-table">
                <table>
                    <thead>
                        <tr>
                            <th>Email</th>
                            <th>Subscription Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="subscribersTable">
                        <tr>
                            <td><EMAIL></td>
                            <td>March 16, 2024</td>
                            <td><span class="status active">Active</span></td>
                            <td>
                                <button class="btn-icon" onclick="sendEmail('<EMAIL>')"><i class="fas fa-envelope"></i></button>
                                <button class="btn-icon delete" onclick="removeSubscriber('<EMAIL>')"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td><EMAIL></td>
                            <td>March 15, 2024</td>
                            <td><span class="status active">Active</span></td>
                            <td>
                                <button class="btn-icon" onclick="sendEmail('<EMAIL>')"><i class="fas fa-envelope"></i></button>
                                <button class="btn-icon delete" onclick="removeSubscriber('<EMAIL>')"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>
    </main>

    <script src="admin-script.js"></script>
</body>
</html>
