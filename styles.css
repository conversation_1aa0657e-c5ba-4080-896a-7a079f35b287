/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-brand i {
    margin-right: 0.5rem;
    font-size: 1.8rem;
}

.nav-menu ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: #ffd700;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background-color: #ffd700;
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.language-switcher .lang-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.language-switcher .lang-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.nav-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Main Content */
.main-content {
    margin-top: 80px;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 100px 0;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-section .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-secondary:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.placeholder-image {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed #dee2e6;
    border-radius: 15px;
    height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 1.1rem;
}

.placeholder-image i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Features Section */
.features-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #333;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.8;
}

/* Content Section */
.content-section {
    padding: 100px 0;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.content-text h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: #333;
}

.content-text p {
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.8;
    font-size: 1.1rem;
}

.feature-list {
    list-style: none;
    margin-bottom: 2rem;
}

.feature-list li {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;
    color: #333;
}

.feature-list i {
    color: #28a745;
    margin-right: 0.8rem;
    font-size: 1.2rem;
}

/* Stats Section */
.stats-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 80px 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Pricing Section */
.pricing-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.pricing-card {
    background: white;
    border-radius: 15px;
    padding: 2.5rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    border: 2px solid transparent;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.pricing-card.featured {
    border-color: #667eea;
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.pricing-header h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.price {
    margin-bottom: 2rem;
}

.currency {
    font-size: 1.5rem;
    vertical-align: top;
    color: #667eea;
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: #333;
}

.period {
    font-size: 1rem;
    color: #666;
}

.pricing-features {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
}

.pricing-features li {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: #333;
}

.pricing-features i {
    color: #28a745;
    margin-right: 0.8rem;
    font-size: 1rem;
}

/* Portfolio Section */
.portfolio-section {
    padding: 100px 0;
    background: white;
}

.portfolio-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.portfolio-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.portfolio-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    padding: 2rem;
    text-align: center;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-info h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: white;
}

.portfolio-info p {
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.portfolio-tech {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.portfolio-tech span {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.portfolio-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.portfolio-links .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
}

.portfolio-cta {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
}

.portfolio-cta p {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #333;
}

/* Blog Section */
.blog-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.blog-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.blog-image {
    height: 200px;
    overflow: hidden;
}

.blog-content {
    padding: 1.5rem;
}

.blog-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.blog-date {
    color: #666;
}

.blog-category {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
}

.blog-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
    line-height: 1.4;
}

.blog-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.read-more {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.read-more:hover {
    color: #764ba2;
}

.blog-cta {
    text-align: center;
    margin-top: 3rem;
}

/* Portfolio Page Styles */
.portfolio-hero,
.blog-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 150px 0 100px;
    text-align: center;
}

.portfolio-hero h1,
.blog-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.portfolio-hero p,
.blog-hero p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.portfolio-main {
    padding: 100px 0;
    background: #f8f9fa;
}

.portfolio-grid-full {
    display: grid;
    gap: 4rem;
}

.portfolio-detail {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 3rem;
}

.project-header {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    padding: 2rem;
    align-items: center;
}

.project-image {
    height: 250px;
}

.project-info h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #333;
}

.project-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.project-category {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
}

.project-date {
    color: #666;
    font-size: 0.9rem;
}

.project-tech {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.project-tech span {
    background: #e9ecef;
    color: #495057;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    border: 1px solid #dee2e6;
}

.project-description {
    padding: 0 2rem 2rem;
}

.project-description h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.project-description h4 {
    font-size: 1.2rem;
    margin: 1.5rem 0 0.8rem;
    color: #667eea;
}

.project-description ul {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
}

.project-description li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

/* Blog Page Styles */
.blog-posts {
    padding: 100px 0;
    background: #f8f9fa;
}

.blog-post-full {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 4rem;
}

.post-header {
    padding: 2rem;
}

.post-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    align-items: center;
}

.post-date {
    color: #666;
    font-size: 0.9rem;
}

.post-category {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
}

.read-time {
    color: #666;
    font-size: 0.9rem;
}

.blog-post-full h2 {
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
    color: #333;
    line-height: 1.3;
}

.post-image {
    height: 300px;
    margin-top: 1rem;
}

.post-content {
    padding: 2rem;
    line-height: 1.8;
}

.post-content h3 {
    font-size: 1.5rem;
    margin: 2rem 0 1rem;
    color: #333;
}

.post-content p {
    margin-bottom: 1.5rem;
    color: #555;
}

.code-example {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 2rem 0;
    border-left: 4px solid #667eea;
}

.code-example h4 {
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.1rem;
}

.code-example pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
}

.post-footer {
    padding: 2rem;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.post-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tag {
    background: #e9ecef;
    color: #495057;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    text-decoration: none;
}

.post-share {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.post-share span {
    color: #666;
    font-weight: 500;
}

.post-share a {
    color: #667eea;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.post-share a:hover {
    color: #764ba2;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 60px 0 20px;
}

.contact-info {
    margin-bottom: 1.5rem;
}

.contact-info p {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;
    color: #bdc3c7;
}

.contact-info i {
    margin-right: 0.8rem;
    width: 20px;
    color: #3498db;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: #ecf0f1;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #3498db;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #34495e;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #3498db;
    transform: translateY(-2px);
}

.newsletter {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.newsletter input {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 25px;
    outline: none;
}

.newsletter .btn {
    padding: 10px 20px;
    font-size: 0.9rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: block;
    }

    .header-controls {
        gap: 0.5rem;
    }

    .language-switcher .lang-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
    }

    .hero-section .container,
    .content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .features-grid,
    .pricing-grid,
    .blog-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: flex-start;
    }

    .newsletter {
        flex-direction: column;
    }

    .pricing-card.featured {
        transform: none;
    }

    .pricing-card.featured:hover {
        transform: translateY(-10px);
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .portfolio-hero h1,
    .blog-hero h1 {
        font-size: 2.5rem;
    }

    .project-header {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .project-image {
        height: 200px;
    }

    .project-info h2 {
        font-size: 1.5rem;
    }

    .blog-post-full h2 {
        font-size: 1.8rem;
    }

    .post-footer {
        flex-direction: column;
        align-items: flex-start;
    }

    .code-example {
        padding: 1rem;
    }

    .code-example pre {
        font-size: 0.8rem;
        padding: 0.8rem;
    }

    .portfolio-filters {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
}
