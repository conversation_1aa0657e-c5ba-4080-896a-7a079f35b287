/* Admin Panel Styles */
.admin-body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', sans-serif;
    background: #f8f9fa;
    color: #333;
}

/* Login Page Styles */
.admin-login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem;
}

.login-card {
    background: white;
    border-radius: 15px;
    padding: 3rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.admin-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.admin-logo i {
    font-size: 2rem;
    color: #667eea;
}

.admin-logo h2 {
    margin: 0;
    color: #333;
    font-weight: 700;
}

.login-header p {
    color: #666;
    margin: 0;
}

.login-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 38px;
    cursor: pointer;
    color: #666;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.checkbox-container input {
    width: auto;
    margin: 0;
}

.login-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.login-btn:hover {
    transform: translateY(-2px);
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 12px;
    border-radius: 8px;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.login-footer {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.back-link {
    color: #667eea;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* Dashboard Layout */
.admin-header {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 2rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
    box-sizing: border-box;
}

.admin-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.admin-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    color: #333;
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logout-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background 0.3s ease;
}

.logout-btn:hover {
    background: #c82333;
}

.admin-sidebar {
    position: fixed;
    left: 0;
    top: 70px;
    width: 250px;
    height: calc(100vh - 70px);
    background: white;
    border-right: 1px solid #e9ecef;
    overflow-y: auto;
    z-index: 999;
}

.admin-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-menu li {
    border-bottom: 1px solid #f8f9fa;
}

.admin-menu a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    color: #666;
    text-decoration: none;
    transition: all 0.3s ease;
}

.admin-menu li.active a,
.admin-menu a:hover {
    background: #f8f9fa;
    color: #667eea;
}

.admin-main {
    margin-left: 250px;
    margin-top: 70px;
    padding: 2rem;
    min-height: calc(100vh - 70px);
}

.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.section-header h1 {
    margin: 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-info h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

.stat-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* Recent Activity */
.recent-activity {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.recent-activity h2 {
    margin: 0 0 1.5rem 0;
    color: #333;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.activity-item i {
    width: 40px;
    height: 40px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.activity-content p {
    margin: 0;
    color: #333;
}

.activity-time {
    color: #666;
    font-size: 0.8rem;
}

/* Content Table */
.content-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.content-table table {
    width: 100%;
    border-collapse: collapse;
}

.content-table th,
.content-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.content-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.content-table tr:hover {
    background: #f8f9fa;
}

/* Status Badges */
.status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status.published,
.status.active {
    background: #d4edda;
    color: #155724;
}

.status.new {
    background: #fff3cd;
    color: #856404;
}

.status.replied {
    background: #d1ecf1;
    color: #0c5460;
}

/* Action Buttons */
.btn-icon {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    color: #666;
    transition: all 0.3s ease;
    margin-right: 0.5rem;
}

.btn-icon:hover {
    background: #f8f9fa;
    color: #333;
}

.btn-icon.delete:hover {
    background: #f8d7da;
    color: #721c24;
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
}

.filter-buttons .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .admin-main {
        margin-left: 0;
        padding: 1rem;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .content-table {
        overflow-x: auto;
    }
}
