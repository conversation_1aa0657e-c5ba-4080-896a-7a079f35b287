-- WebsiteDeveloper0002 Database Schema
-- Professional Web Development Services Database

CREATE DATABASE IF NOT EXISTS websitedeveloper0002_db;
USE websitedeveloper0002_db;

-- Admin users table
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL,
    full_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    role ENUM('admin', 'editor') DEFAULT 'admin',
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive') DEFAULT 'active'
);

-- Contact queries table
CREATE TABLE contact_queries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    subject VARCHAR(200),
    message TEXT NOT NULL,
    service_type ENUM('business_premium', 'ecommerce', 'custom', 'other') DEFAULT 'other',
    status ENUM('new', 'in_progress', 'replied', 'closed') DEFAULT 'new',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    assigned_to INT,
    replied_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIGN KEY (assigned_to) REFERENCES admin_users(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_email (email)
);

-- Newsletter subscribers table
CREATE TABLE newsletter_subscribers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(100) UNIQUE NOT NULL,
    status ENUM('active', 'unsubscribed', 'bounced') DEFAULT 'active',
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unsubscribed_at TIMESTAMP NULL,
    source VARCHAR(50) DEFAULT 'website',
    ip_address VARCHAR(45),
    user_agent TEXT,
    INDEX idx_status (status),
    INDEX idx_subscribed_at (subscribed_at)
);

-- Blog posts table
CREATE TABLE blog_posts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    excerpt TEXT,
    content LONGTEXT NOT NULL,
    featured_image VARCHAR(255),
    category VARCHAR(50) NOT NULL,
    tags JSON,
    meta_title VARCHAR(200),
    meta_description TEXT,
    meta_keywords TEXT,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    author_id INT NOT NULL,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    views_count INT DEFAULT 0,
    FOREIGN KEY (author_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_status (status),
    INDEX idx_category (category),
    INDEX idx_published_at (published_at),
    INDEX idx_slug (slug)
);

-- Portfolio projects table
CREATE TABLE portfolio_projects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    featured_image VARCHAR(255),
    gallery_images JSON,
    category ENUM('ecommerce', 'business', 'restaurant', 'healthcare', 'education', 'other') NOT NULL,
    technologies JSON,
    project_url VARCHAR(255),
    client_name VARCHAR(100),
    completion_date DATE,
    project_duration VARCHAR(50),
    key_features JSON,
    results_achieved JSON,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_category (category),
    INDEX idx_sort_order (sort_order)
);

-- Email templates table
CREATE TABLE email_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    subject VARCHAR(200) NOT NULL,
    html_content LONGTEXT NOT NULL,
    text_content TEXT,
    variables JSON,
    template_type ENUM('contact_thank_you', 'newsletter_welcome', 'admin_notification', 'custom') NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_template_type (template_type),
    INDEX idx_status (status)
);

-- Email logs table
CREATE TABLE email_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    to_email VARCHAR(100) NOT NULL,
    from_email VARCHAR(100) NOT NULL,
    subject VARCHAR(200) NOT NULL,
    template_id INT,
    email_type VARCHAR(50) NOT NULL,
    status ENUM('sent', 'failed', 'bounced', 'opened', 'clicked') DEFAULT 'sent',
    error_message TEXT,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    opened_at TIMESTAMP NULL,
    clicked_at TIMESTAMP NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIGN KEY (template_id) REFERENCES email_templates(id) ON DELETE SET NULL,
    INDEX idx_to_email (to_email),
    INDEX idx_email_type (email_type),
    INDEX idx_status (status),
    INDEX idx_sent_at (sent_at)
);

-- Email statistics table
CREATE TABLE email_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email_type VARCHAR(50) NOT NULL,
    sent_count INT DEFAULT 0,
    opened_count INT DEFAULT 0,
    clicked_count INT DEFAULT 0,
    bounced_count INT DEFAULT 0,
    sent_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_type_date (email_type, sent_date),
    INDEX idx_sent_date (sent_date),
    INDEX idx_email_type (email_type)
);

-- Website settings table
CREATE TABLE website_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key)
);

-- Contact form replies table
CREATE TABLE contact_replies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contact_query_id INT NOT NULL,
    admin_id INT NOT NULL,
    reply_message TEXT NOT NULL,
    reply_type ENUM('email', 'phone', 'meeting') DEFAULT 'email',
    replied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (contact_query_id) REFERENCES contact_queries(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_contact_query_id (contact_query_id),
    INDEX idx_replied_at (replied_at)
);

-- Insert default admin user
INSERT INTO admin_users (username, password_hash, email, full_name, role) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Admin User', 'admin');
-- Default password: websitedeveloper0002@admin (hash this in production)

-- Insert default email templates
INSERT INTO email_templates (name, subject, html_content, template_type) VALUES 
(
    'contact_thank_you',
    'Thank you for contacting WebsiteDeveloper0002',
    '<!DOCTYPE html><html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333}.container{max-width:600px;margin:0 auto;padding:20px}.header{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:white;padding:30px;text-align:center}.content{padding:30px;background:#f9f9f9}.footer{padding:20px;text-align:center;background:#333;color:white}</style></head><body><div class="container"><div class="header"><h1>Thank You for Your Inquiry!</h1><p>WebsiteDeveloper0002 - Professional Web Development Services</p></div><div class="content"><h2>Dear {{name}},</h2><p>Thank you for reaching out to us! We have received your inquiry and our team will review it carefully.</p><p><strong>Reference ID:</strong> WD002-{{contact_id}}</p><p>We typically respond within 24 hours during business days.</p><p>Best regards,<br><strong>WebsiteDeveloper0002 Team</strong></p></div><div class="footer"><p>&copy; 2024 WebsiteDeveloper0002. All rights reserved.</p></div></div></body></html>',
    'contact_thank_you'
),
(
    'newsletter_welcome',
    'Welcome to WebsiteDeveloper0002 Newsletter!',
    '<!DOCTYPE html><html><head><meta charset="UTF-8"><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333}.container{max-width:600px;margin:0 auto;padding:20px}.header{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:white;padding:30px;text-align:center}.content{padding:30px;background:#f9f9f9}.footer{padding:20px;text-align:center;background:#333;color:white}</style></head><body><div class="container"><div class="header"><h1>🎉 Welcome to Our Newsletter!</h1><p>WebsiteDeveloper0002 - Stay Updated with Latest Web Development Trends</p></div><div class="content"><h2>Thank you for subscribing!</h2><p>You are now part of our community of web development enthusiasts.</p><p><strong>Subscriber ID:</strong> WD002-NL-{{subscriber_id}}</p><p>Best regards,<br><strong>WebsiteDeveloper0002 Team</strong></p></div><div class="footer"><p>&copy; 2024 WebsiteDeveloper0002. All rights reserved.</p></div></div></body></html>',
    'newsletter_welcome'
);

-- Insert default website settings
INSERT INTO website_settings (setting_key, setting_value, setting_type, description) VALUES 
('site_name', 'WebsiteDeveloper0002', 'text', 'Website name'),
('admin_email', '<EMAIL>', 'text', 'Admin email address'),
('contact_email', '<EMAIL>', 'text', 'Contact email address'),
('phone_number', '+91 9876543210', 'text', 'Contact phone number'),
('whatsapp_number', '+91 9876543210', 'text', 'WhatsApp number'),
('email_monthly_limit', '500', 'number', 'Monthly email sending limit'),
('recaptcha_site_key', '', 'text', 'reCAPTCHA site key'),
('recaptcha_secret_key', '', 'text', 'reCAPTCHA secret key'),
('smtp_host', 'smtp.gmail.com', 'text', 'SMTP host'),
('smtp_port', '587', 'number', 'SMTP port'),
('smtp_username', '', 'text', 'SMTP username'),
('smtp_password', '', 'text', 'SMTP password'),
('google_analytics_id', '', 'text', 'Google Analytics tracking ID'),
('facebook_url', '', 'text', 'Facebook page URL'),
('twitter_url', '', 'text', 'Twitter profile URL'),
('linkedin_url', '', 'text', 'LinkedIn profile URL'),
('instagram_url', '', 'text', 'Instagram profile URL'),
('youtube_url', '', 'text', 'YouTube channel URL');

-- Create indexes for better performance
CREATE INDEX idx_contact_queries_composite ON contact_queries(status, created_at);
CREATE INDEX idx_email_logs_composite ON email_logs(email_type, status, sent_at);
CREATE INDEX idx_newsletter_composite ON newsletter_subscribers(status, subscribed_at);

-- Create views for reporting
CREATE VIEW contact_summary AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_contacts,
    SUM(CASE WHEN status = 'new' THEN 1 ELSE 0 END) as new_contacts,
    SUM(CASE WHEN status = 'replied' THEN 1 ELSE 0 END) as replied_contacts,
    SUM(CASE WHEN service_type = 'business_premium' THEN 1 ELSE 0 END) as business_inquiries,
    SUM(CASE WHEN service_type = 'ecommerce' THEN 1 ELSE 0 END) as ecommerce_inquiries
FROM contact_queries 
GROUP BY DATE(created_at)
ORDER BY date DESC;

CREATE VIEW email_summary AS
SELECT 
    email_type,
    DATE(sent_at) as date,
    COUNT(*) as total_sent,
    SUM(CASE WHEN status = 'opened' THEN 1 ELSE 0 END) as total_opened,
    SUM(CASE WHEN status = 'clicked' THEN 1 ELSE 0 END) as total_clicked,
    SUM(CASE WHEN status = 'bounced' THEN 1 ELSE 0 END) as total_bounced,
    ROUND((SUM(CASE WHEN status = 'opened' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as open_rate
FROM email_logs 
GROUP BY email_type, DATE(sent_at)
ORDER BY date DESC, email_type;
