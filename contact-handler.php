<?php
// Contact Form Handler with Email System
// WebsiteDeveloper0002 - Professional Contact Management

// Database configuration
$host = 'localhost';
$dbname = 'websitedeveloper0002_db';
$username = 'your_db_username';
$password = 'your_db_password';

// Email configuration
$smtp_host = 'smtp.gmail.com';
$smtp_port = 587;
$smtp_username = '<EMAIL>';
$smtp_password = 'your_app_password';
$from_email = '<EMAIL>';
$from_name = 'WebsiteDeveloper0002';

// reCAPTCHA configuration
$recaptcha_secret = 'your_recaptcha_secret_key';

try {
    // Database connection
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        
        // Verify reCAPTCHA
        if (isset($_POST['g-recaptcha-response'])) {
            $recaptcha_response = $_POST['g-recaptcha-response'];
            $verify_url = "https://www.google.com/recaptcha/api/siteverify";
            $verify_data = array(
                'secret' => $recaptcha_secret,
                'response' => $recaptcha_response,
                'remoteip' => $_SERVER['REMOTE_ADDR']
            );
            
            $verify_response = file_get_contents($verify_url . '?' . http_build_query($verify_data));
            $verify_result = json_decode($verify_response, true);
            
            if (!$verify_result['success']) {
                throw new Exception('reCAPTCHA verification failed');
            }
        }
        
        // Sanitize and validate input
        $name = filter_var(trim($_POST['name']), FILTER_SANITIZE_STRING);
        $email = filter_var(trim($_POST['email']), FILTER_SANITIZE_EMAIL);
        $phone = filter_var(trim($_POST['phone']), FILTER_SANITIZE_STRING);
        $subject = filter_var(trim($_POST['subject']), FILTER_SANITIZE_STRING);
        $message = filter_var(trim($_POST['message']), FILTER_SANITIZE_STRING);
        $service_type = filter_var(trim($_POST['service_type']), FILTER_SANITIZE_STRING);
        
        // Validate required fields
        if (empty($name) || empty($email) || empty($message)) {
            throw new Exception('Please fill in all required fields');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Please enter a valid email address');
        }
        
        // Insert contact query into database
        $stmt = $pdo->prepare("
            INSERT INTO contact_queries (name, email, phone, subject, message, service_type, created_at, status) 
            VALUES (?, ?, ?, ?, ?, ?, NOW(), 'new')
        ");
        
        $stmt->execute([$name, $email, $phone, $subject, $message, $service_type]);
        $contact_id = $pdo->lastInsertId();
        
        // Send thank you email to customer
        sendThankYouEmail($email, $name, $contact_id);
        
        // Send notification email to admin
        sendAdminNotification($name, $email, $phone, $subject, $message, $service_type, $contact_id);
        
        // Update email statistics
        updateEmailStats('contact_thank_you');
        updateEmailStats('admin_notification');
        
        // Return success response
        echo json_encode([
            'success' => true,
            'message' => 'Thank you for your inquiry! We will get back to you within 24 hours.',
            'contact_id' => $contact_id
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action'])) {
        
        // Handle newsletter subscription
        if ($_GET['action'] === 'subscribe') {
            $email = filter_var(trim($_GET['email']), FILTER_SANITIZE_EMAIL);
            
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Please enter a valid email address');
            }
            
            // Check if already subscribed
            $stmt = $pdo->prepare("SELECT id FROM newsletter_subscribers WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->rowCount() > 0) {
                echo json_encode([
                    'success' => false,
                    'message' => 'This email is already subscribed to our newsletter'
                ]);
                exit;
            }
            
            // Insert new subscriber
            $stmt = $pdo->prepare("
                INSERT INTO newsletter_subscribers (email, subscribed_at, status) 
                VALUES (?, NOW(), 'active')
            ");
            
            $stmt->execute([$email]);
            $subscriber_id = $pdo->lastInsertId();
            
            // Send welcome email
            sendNewsletterWelcomeEmail($email, $subscriber_id);
            
            // Update email statistics
            updateEmailStats('newsletter_welcome');
            
            echo json_encode([
                'success' => true,
                'message' => 'Thank you for subscribing! Check your email for confirmation.',
                'subscriber_id' => $subscriber_id
            ]);
        }
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Function to send thank you email
function sendThankYouEmail($to_email, $name, $contact_id) {
    global $from_email, $from_name;
    
    $subject = "Thank you for contacting WebsiteDeveloper0002";
    
    $html_body = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
            .content { padding: 30px; background: #f9f9f9; }
            .footer { padding: 20px; text-align: center; background: #333; color: white; }
            .btn { background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Thank You for Your Inquiry!</h1>
                <p>WebsiteDeveloper0002 - Professional Web Development Services</p>
            </div>
            <div class='content'>
                <h2>Dear $name,</h2>
                <p>Thank you for reaching out to us! We have received your inquiry and our team will review it carefully.</p>
                
                <p><strong>Your inquiry details:</strong></p>
                <p><strong>Reference ID:</strong> WD002-$contact_id</p>
                <p><strong>Email:</strong> $to_email</p>
                
                <p>We typically respond to all inquiries within 24 hours during business days. Our team will contact you soon with detailed information about your requirements.</p>
                
                <p>In the meantime, feel free to:</p>
                <ul>
                    <li>Check out our <a href='https://websitedeveloper0002.in/portfolio.html'>portfolio</a> to see our recent work</li>
                    <li>Read our <a href='https://websitedeveloper0002.in/blog.html'>blog</a> for web development tips</li>
                    <li>Follow us on social media for updates</li>
                </ul>
                
                <p>If you have any urgent questions, you can reach us at:</p>
                <p>📧 Email: <EMAIL><br>
                📱 Phone: +91 9876543210<br>
                💬 WhatsApp: +91 9876543210</p>
                
                <p>Thank you for choosing WebsiteDeveloper0002!</p>
                
                <p>Best regards,<br>
                <strong>WebsiteDeveloper0002 Team</strong><br>
                Professional Web Development Services</p>
            </div>
            <div class='footer'>
                <p>&copy; 2024 WebsiteDeveloper0002. All rights reserved.</p>
                <p>Professional Web Development Services in India</p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    // Send email using PHP mail function (in production, use PHPMailer or similar)
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: $from_name <$from_email>" . "\r\n";
    $headers .= "Reply-To: $from_email" . "\r\n";
    
    mail($to_email, $subject, $html_body, $headers);
}

// Function to send admin notification
function sendAdminNotification($name, $email, $phone, $subject, $message, $service_type, $contact_id) {
    global $from_email, $from_name;
    
    $admin_email = '<EMAIL>';
    $admin_subject = "New Contact Query - WebsiteDeveloper0002";
    
    $html_body = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .info-box { background: white; padding: 15px; margin: 10px 0; border-left: 4px solid #667eea; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>🚨 New Contact Query</h1>
                <p>WebsiteDeveloper0002 Admin Panel</p>
            </div>
            <div class='content'>
                <h2>Contact Details:</h2>
                
                <div class='info-box'>
                    <strong>Reference ID:</strong> WD002-$contact_id<br>
                    <strong>Name:</strong> $name<br>
                    <strong>Email:</strong> $email<br>
                    <strong>Phone:</strong> $phone<br>
                    <strong>Service Type:</strong> $service_type<br>
                    <strong>Subject:</strong> $subject
                </div>
                
                <div class='info-box'>
                    <strong>Message:</strong><br>
                    $message
                </div>
                
                <p><strong>Action Required:</strong> Please respond to this inquiry within 24 hours.</p>
                
                <p><a href='https://websitedeveloper0002.in/admin/dashboard.html' style='background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;'>View in Admin Panel</a></p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: $from_name <$from_email>" . "\r\n";
    $headers .= "Reply-To: $email" . "\r\n";
    
    mail($admin_email, $admin_subject, $html_body, $headers);
}

// Function to send newsletter welcome email
function sendNewsletterWelcomeEmail($to_email, $subscriber_id) {
    global $from_email, $from_name;
    
    $subject = "Welcome to WebsiteDeveloper0002 Newsletter!";
    
    $html_body = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
            .content { padding: 30px; background: #f9f9f9; }
            .footer { padding: 20px; text-align: center; background: #333; color: white; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>🎉 Welcome to Our Newsletter!</h1>
                <p>WebsiteDeveloper0002 - Stay Updated with Latest Web Development Trends</p>
            </div>
            <div class='content'>
                <h2>Thank you for subscribing!</h2>
                <p>You're now part of our community of web development enthusiasts. Here's what you can expect:</p>
                
                <ul>
                    <li>📚 Latest web development tutorials and tips</li>
                    <li>🚀 New project showcases and case studies</li>
                    <li>💡 Industry insights and trends</li>
                    <li>🎯 Exclusive offers and early access to services</li>
                    <li>📈 SEO and digital marketing strategies</li>
                </ul>
                
                <p>We send newsletters weekly with valuable content to help you stay ahead in the digital world.</p>
                
                <p><strong>Subscriber ID:</strong> WD002-NL-$subscriber_id</p>
                
                <p>Follow us on social media for daily updates:</p>
                <p>🔗 Website: https://websitedeveloper0002.in<br>
                📧 Email: <EMAIL><br>
                📱 Phone: +91 9876543210</p>
                
                <p>Best regards,<br>
                <strong>WebsiteDeveloper0002 Team</strong></p>
                
                <p><small>You can unsubscribe at any time by clicking <a href='https://websitedeveloper0002.in/unsubscribe.php?id=$subscriber_id'>here</a>.</small></p>
            </div>
            <div class='footer'>
                <p>&copy; 2024 WebsiteDeveloper0002. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: $from_name <$from_email>" . "\r\n";
    $headers .= "Reply-To: $from_email" . "\r\n";
    
    mail($to_email, $subject, $html_body, $headers);
}

// Function to update email statistics
function updateEmailStats($email_type) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        INSERT INTO email_stats (email_type, sent_count, sent_date) 
        VALUES (?, 1, CURDATE()) 
        ON DUPLICATE KEY UPDATE sent_count = sent_count + 1
    ");
    
    $stmt->execute([$email_type]);
}
?>
